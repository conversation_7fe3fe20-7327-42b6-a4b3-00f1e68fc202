"use client"

import React, { useState, useEffect, useRef } from "react"
import Image from "next/image"
import {
  Sell<PERSON>MallBadge,
  SellzioStarBadge,
  SellzioStarLiteBadge,
  SellzioCodBadge,
  SellzioDiscountBadge,
  SellzioDiscountCornerBadge,
  SellzioRatingStars,
  SellzioShippingBadge,
  SellzioTerlarisBadge,
  // SellzioLiveBadge, // Dihapus sesuai permintaan user
  Sell<PERSON><PERSON><PERSON><PERSON>h<PERSON>iTokoBadge,
  SellzioKomisiXtraBadge,
} from "./sellzio-badges"

export interface SellzioImageSliderCardProps {
  id: number
  name: string
  price: string
  originalPrice?: string
  discount?: string
  images: string[] // Array of image URLs
  rating?: number
  sold?: number
  hasCod?: boolean
  isMall?: boolean
  badgeType?: "mall" | "star" | "star-lite" | "none"
  shipping?: string
  onClick?: () => void
}

export const SellzioImageSliderCard = ({
  id,
  name,
  price,
  originalPrice,
  discount,
  images = [],
  rating = 0,
  sold = 0,
  hasCod = false,
  isMall = false,
  badgeType = "none",
  shipping = "Pengiriman Instan",
  onClick
}: SellzioImageSliderCardProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [imageLoaded, setImageLoaded] = useState<boolean[]>(Array(images.length).fill(false))
  const [autoplayPaused, setAutoplayPaused] = useState(false)
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Ensure we have valid images and have at least 3 images (duplicate if needed)
  const validImages = images && images.length > 0 ? [...images] : ["/api/placeholder/200/200"]

  // If we have less than 3 images, duplicate the existing ones to reach 3
  if (validImages.length < 3) {
    const originalLength = validImages.length
    for (let i = 0; i < 3 - originalLength; i++) {
      validImages.push(validImages[i % originalLength])
    }
  }

  // Setup autoplay like Velozio
  useEffect(() => {
    if (validImages.length <= 1 || autoplayPaused) return

    const startAutoplay = () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current)
      }

      autoplayTimerRef.current = setInterval(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % validImages.length)
      }, 3000) // 3 seconds like Velozio
    }

    startAutoplay()

    return () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current)
      }
    }
  }, [validImages.length, autoplayPaused])

  const getShippingType = (): "instan" | "gratis" | "reguler" => {
    if (shipping?.toLowerCase().includes("instan")) return "instan"
    if (shipping?.toLowerCase().includes("gratis")) return "gratis"
    return "reguler"
  }

  const handleImageLoad = (index: number) => {
    setImageLoaded((prev) => {
      const newState = [...prev]
      newState[index] = true
      return newState
    })
  }

  const handleDotClick = (index: number, e: React.MouseEvent) => {
    e.stopPropagation()
    setCurrentImageIndex(index)
    setAutoplayPaused(true)

    // Resume autoplay after 5 seconds
    setTimeout(() => {
      setAutoplayPaused(false)
    }, 5000)
  }

  return (
    <div className="sellzio-image-slider-card" onClick={onClick}>
      {/* Image Container with Slider - Velozio Style */}
      <div className="sellzio-image-container">
        {/* Badge diskon di pojok kanan atas seperti card lainnya */}
        {originalPrice && discount && <SellzioDiscountCornerBadge discount={discount} />}

        {/* Images with Velozio-style transitions */}
        {validImages.map((imageSrc, index) => (
          <div
            key={index}
            className={`sellzio-image-slide ${
              currentImageIndex === index
                ? "opacity-100 z-10 translate-x-0"
                : index === (currentImageIndex + 1) % validImages.length
                ? "opacity-0 z-0 translate-x-full"
                : index === (currentImageIndex - 1 + validImages.length) % validImages.length
                ? "opacity-0 z-0 -translate-x-full"
                : "opacity-0 z-0"
            }`}
          >
            <Image
              src={imageSrc}
              alt={`${name} - Image ${index + 1}`}
              fill
              className={`sellzio-product-image ${
                imageLoaded[index] ? "opacity-100" : "opacity-0"
              }`}
              onLoad={() => handleImageLoad(index)}
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              priority={index === currentImageIndex}
            />
            {!imageLoaded[index] && (
              <div className="sellzio-image-placeholder">
                <div className="sellzio-loading-shimmer"></div>
              </div>
            )}
          </div>
        ))}

        {/* Dots Indicator - Velozio Style */}
        {validImages.length > 1 && (
          <div className="sellzio-image-dots">
            {validImages.map((_, index) => (
              <button
                key={index}
                className={`sellzio-dot ${index === currentImageIndex ? "active" : ""}`}
                onClick={(e) => handleDotClick(index, e)}
                aria-label={`Go to image ${index + 1}`}
                aria-current={currentImageIndex === index ? "true" : "false"}
              />
            ))}
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="sellzio-product-info">
        {/* Product Name with badges */}
        <h3 className="sellzio-product-name">
          {/* Render badges based on priority */}
          {badgeType === "mall" || isMall ? (
            <SellzioMallBadge />
          ) : badgeType === "star" ? (
            <SellzioStarBadge />
          ) : badgeType === "star-lite" ? (
            <SellzioStarLiteBadge />
          ) : null}
          {name}
        </h3>

        {/* Special badges dihapus karena badgeType hanya mendukung mall, star, star-lite, none */}

        {/* Rating di bawah special badges dengan COD badge */}
        <div className="sellzio-rating-container">
          <SellzioRatingStars rating={rating} />
          {hasCod && <SellzioCodBadge />}
        </div>

        {/* Price dengan terjual di kanan */}
        <div className="sellzio-price-container" data-long-price={price.length > 10}>
          <div className="sellzio-price-left">
            <div className="sellzio-current-price">{price}</div>
            {originalPrice && discount && (
              <div className="sellzio-discount-ticket">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                  <path fillRule="evenodd" d="M1.5 6.375c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v3.026a.75.75 0 01-.375.65 2.249 2.249 0 000 3.898.75.75 0 01.375.65v3.026c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 17.625v-3.026a.75.75 0 01.374-.65 2.249 2.249 0 000-3.898.75.75 0 01-.374-.65V6.375zm15-1.125a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V6a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0v-.75zm-.75 3a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V15a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0V18zM6 12a.75.75 0 01.75-.75H12a.75.75 0 010 1.5H6.75A.75.75 0 016 12zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z" clipRule="evenodd" />
                  <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                </svg>
              </div>
            )}
          </div>
          <div className="sellzio-sold-count">Terjual {sold}</div>
        </div>

        {/* Shipping di bawah harga */}
        <SellzioShippingBadge type={getShippingType()} city="Bandung" />
      </div>

      <style jsx>{`
        .sellzio-image-slider-card {
          background-color: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          cursor: pointer;
          transition: all 0.2s ease;
          width: 100%;
          border: 1px solid #f3f4f6;
        }

        .sellzio-image-slider-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .sellzio-image-container {
          position: relative;
          width: 100%;
          padding-top: 100%; /* 1:1 Aspect Ratio like Velozio */
          overflow: hidden;
          background-color: #f9fafb;
          z-index: 1; /* Base z-index untuk container */
        }

        .sellzio-image-slide {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          transition: all 0.3s ease;
          z-index: 2; /* Image slides di atas container */
        }

        .sellzio-product-image {
          object-fit: contain;
          transition: opacity 0.3s ease;
          padding: 8px;
        }

        .sellzio-image-placeholder {
          position: absolute;
          inset: 0;
          background-color: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1; /* Placeholder di bawah image slides */
        }

        /* Badge diskon sekarang di luar container gambar, tidak perlu styling khusus di sini */

        .sellzio-loading-shimmer {
          width: 60%;
          height: 60%;
          background: linear-gradient(
            90deg,
            #f3f4f6 25%,
            #e5e7eb 50%,
            #f3f4f6 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }



        .sellzio-image-dots {
          position: absolute;
          bottom: 12px;
          left: 0;
          right: 0;
          z-index: 3; /* Dots di atas image slides (z-index 2) tapi di bawah badge (z-index 30) */
          display: flex;
          justify-content: center;
        }

        .sellzio-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin: 0 2px;
          border: none;
          background-color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .sellzio-dot.active {
          background-color: #ee4d2d;
        }

        .sellzio-dot:hover {
          background-color: #ee4d2d;
        }

        .sellzio-product-info {
          padding: 12px;
        }

        .sellzio-product-name {
          font-size: 13px;
          font-weight: 500;
          color: #374151;
          line-height: 1.4;
          margin-bottom: 8px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* Rating container dengan COD badge */
        .sellzio-rating-container {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 8px;
        }

        /* Force rating to be visible */
        .sellzio-product-info > div:nth-child(3) {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }



        .sellzio-product-meta {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 11px;
          color: #6b7280;
        }

        .sellzio-meta-divider {
          width: 1px;
          height: 12px;
          background-color: #d1d5db;
          margin: 0 8px;
        }

        .sellzio-sold-count {
          font-size: 11px;
          color: #6b7280;
        }

        .sellzio-price-container {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 8px;
        }

        .sellzio-current-price {
          font-size: 14px;
          font-weight: 700;
          color: #f97316;
        }

        .sellzio-discount-ticket {
          color: #f97316;
          display: flex;
          align-items: center;
        }

        @media (max-width: 768px) {
          .sellzio-image-slider-card {
            border-radius: 6px;
          }

          .sellzio-product-info {
            padding: 10px;
          }

          .sellzio-product-name {
            font-size: 12px;
          }

          .sellzio-current-price {
            font-size: 13px;
            flex-shrink: 1;
            min-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .sellzio-sold-count {
            font-size: 9px;
            flex-shrink: 0;
            white-space: nowrap;
          }

          .sellzio-price-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            flex-wrap: nowrap;
          }

          .sellzio-price-left {
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            min-width: 0;
            overflow: hidden;
          }

          .sellzio-discount-ticket {
            flex-shrink: 0;
          }

          /* Desktop/Tablet: Semua elemen wajib ada dan sejajar */
          @media (min-width: 769px) {
            .sellzio-price-container {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 8px;
            }

            .sellzio-price-left {
              display: flex;
              align-items: center;
              gap: 8px;
            }

            .sellzio-discount-ticket {
              display: flex !important;
            }

            .sellzio-sold-count {
              display: block !important;
            }
          }

          /* Mobile priority: harga panjang = tiket, harga pendek = terjual */
          @media (max-width: 768px) {
            .sellzio-current-price {
              max-width: 110px;
            }

            /* Jika harga panjang (>10 karakter), sembunyikan terjual, tampilkan tiket */
            .sellzio-price-container[data-long-price="true"] .sellzio-sold-count {
              display: none;
            }

            .sellzio-price-container[data-long-price="true"] .sellzio-discount-ticket {
              display: flex;
            }

            /* Jika harga pendek, sembunyikan tiket, tampilkan terjual */
            .sellzio-price-container[data-long-price="false"] .sellzio-discount-ticket {
              display: none;
            }

            /* Harga pendek wajib sejajar dengan terjual */
            .sellzio-price-container[data-long-price="false"] {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 8px;
              flex-wrap: nowrap;
            }
          }
        }
      `}</style>
    </div>
  )
}
